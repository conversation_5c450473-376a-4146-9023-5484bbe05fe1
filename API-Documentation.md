# API 文档

本文档详细描述了基金策略回测系统中所有API的入参和出参格式。

## 基金相关API

### 1. 基金搜索 API

**端点**: `GET /api/fund/search`

**描述**: 根据关键词搜索基金

**请求参数**:
- `q` (string, 必填): 搜索关键词，可以是基金名称或代码
- `page` (number, 可选): 页码，默认为1
- `size` (number, 可选): 每页数量，默认为20

**响应格式**:
```typescript
{
  success: boolean;
  data: Fund[];
  total: number;
  page: number;
  size: number;
  error?: string; // 仅在失败时返回
}

interface Fund {
  id: string;
  name: string;
  code: string;
  type: "stock" | "bond" | "hybrid" | "index" | "money";
  company: string;
  description: string;
}
```

**示例请求**:
```
GET /api/fund/search?q=华夏&page=1&size=10
```

**示例响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "000001-0",
      "name": "华夏成长混合",
      "code": "000001",
      "type": "hybrid",
      "company": "华夏基金",
      "description": "华夏基金管理的混合型基金"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10
}
```

### 2. 基金数据获取 API

**端点**: `GET /api/fund`

**描述**: 获取指定基金的历史净值数据

**请求参数**:
- `code` (string, 必填): 6位基金代码
- `startDate` (string, 可选): 开始日期，格式YYYY-MM-DD
- `endDate` (string, 可选): 结束日期，格式YYYY-MM-DD

**响应格式**:
```typescript
{
  success: boolean;
  data: FundData[];
  meta: {
    fundCode: string;
    count: number;
  };
  error?: string; // 仅在失败时返回
}

interface FundData {
  date: string;
  netAssetValue: number;
  accumulatedValue: number;
  dailyGrowthRate?: number;
}
```

## 指数相关API

### 3. 指数搜索 API

**端点**: `GET /api/index/search`

**描述**: 根据关键词搜索指数

**请求参数**:
- `q` (string, 必填): 搜索关键词，可以是指数名称或代码
- `page` (number, 可选): 页码，默认为1
- `size` (number, 可选): 每页数量，默认为10

**响应格式**:
```typescript
{
  success: boolean;
  data: IndexItem[];
  total: number;
  page: number;
  size: number;
  error?: string; // 仅在失败时返回
}

interface IndexItem {
  id: string;
  name: string;
  code: string;
  description: string;
  type: string;
  classify: string;
  currency: string;
  region: string;
  publishDate: string;
  baseDate: string;
  basePoint: number;
}
```

**示例请求**:
```
GET /api/index/search?q=中证500&page=1&size=10
```

**示例响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "000905",
      "name": "中证500指数",
      "code": "000905",
      "description": "股票指数 | 基准日期: 2004-12-31 | 基准点数: 1000",
      "type": "股票指数",
      "classify": "股票指数",
      "currency": "CNY",
      "region": "中国",
      "publishDate": "2005-01-04",
      "baseDate": "2004-12-31",
      "basePoint": 1000
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10
}
```

## 错误处理

所有API都遵循统一的错误处理格式：

**客户端错误 (4xx)**:
```json
{
  "success": false,
  "error": "错误描述信息"
}
```

**服务器错误 (5xx)**:
```json
{
  "success": false,
  "error": "服务器内部错误或第三方API错误"
}
```

## 常见错误码

- `400 Bad Request`: 请求参数错误或缺失
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误或第三方API异常

## 数据源说明

### 基金数据源
- **CSRC API**: 中国证监会基金信息披露API
- **Baidu API**: 百度股市通基金净值数据API

### 指数数据源
- **CSIndex API**: 中证指数有限公司官方API

## 注意事项

1. 所有搜索API都要求提供搜索关键词，不支持空查询
2. 分页参数都是可选的，有合理的默认值
3. 日期参数格式必须为 YYYY-MM-DD
4. 基金代码必须为6位数字
5. API响应都包含 `success` 字段用于判断请求是否成功
6. 错误信息通过 `error` 字段返回，仅在失败时存在
