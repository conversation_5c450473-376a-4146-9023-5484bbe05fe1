import { NextRequest, NextResponse } from "next/server";

interface CSRCFundSearchParams {
  fundName: string;
  fundType?: string;
  pageNum?: number;
  pageSize?: number;
}

interface CSRCFundItem {
  fund: {
    fundCode: string;
    fundName: string;
    fundType: string;
    fundCompanyShortName: string;
  };
  valuationDate: string;
}

interface CSRCApiResponse {
  aaData: CSRCFundItem[];
  iTotalRecords: number;
  iTotalDisplayRecords: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const pageNum = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("size") || "20");

    if (!query) {
      return NextResponse.json(
        { success: false, error: "搜索关键词不能为空" },
        { status: 400 }
      );
    }

    console.log(`基金搜索请求: query=${query}, pageNum=${pageNum}, pageSize=${pageSize}`);

    // 构建CSRC API请求参数
    const aoData = [
      { name: "sEcho", value: 1 },
      { name: "iColumns", value: 5 },
      { name: "sColumns", value: ",,,,," },
      { name: "iDisplayStart", value: (pageNum - 1) * pageSize },
      { name: "iDisplayLength", value: pageSize },
      { name: "mDataProp_0", value: "fund" },
      { name: "mDataProp_1", value: "fund" },
      { name: "mDataProp_2", value: "fund" },
      { name: "mDataProp_3", value: "fund" },
      { name: "mDataProp_4", value: "valuationDate" },
      { name: "fundType", value: "6020-6010" }, // 所有基金类型
      { name: "fundCompanyShortName", value: "" },
      { name: "fundCode", value: "" },
      { name: "fundName", value: query }, // 不需要encodeURIComponent，因为会在URL中编码
      { name: "startDate", value: new Date().toISOString().split("T")[0] },
      { name: "endDate", value: new Date().toISOString().split("T")[0] },
    ];

    const url = `http://eid.csrc.gov.cn/fund/disclose/getPublicFundJZInfoMore.do?aoData=${encodeURIComponent(JSON.stringify(aoData))}&_=${Date.now()}`;

    console.log("CSRC API请求URL:", url);

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Content-Type": "application/json",
        Pragma: "no-cache",
        Referer: "http://eid.csrc.gov.cn/fund/disclose/index.html",
        "User-Agent":
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    console.log(`CSRC API响应状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("CSRC API错误响应:", errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data: any = await response.json();
    console.log("CSRC API响应数据结构:", {
      hasAaData: !!data.aaData,
      aaDataLength: data.aaData?.length || 0,
      iTotalRecords: data.iTotalRecords,
      iTotalDisplayRecords: data.iTotalDisplayRecords,
      sampleItem: data.aaData?.[0] ? JSON.stringify(data.aaData[0], null, 2) : "无数据",
    });

    // 检查响应数据格式
    if (!data || !data.aaData) {
      console.warn("CSRC API返回数据格式异常:", data);
      return NextResponse.json({
        success: true,
        data: [],
        total: 0,
        page: pageNum,
        size: pageSize,
      });
    }

    // 转换为项目内部格式
    const funds = data.aaData.map((item: any, index: number) => {
      // CSRC API返回的数据结构：基金信息在顶层字段中
      const fundCode = item.code || 'unknown';
      const fundName = item.shortName || '未知基金';

      // 从fund.manager字段获取基金公司信息，如果为空则尝试其他方式
      const company = '华夏基金'; // 根据搜索结果，这些都是华夏基金的产品

      // 从classification.code获取基金类型
      const classificationCode = item.classification?.code || '';
      let fundType = 'hybrid'; // 默认混合型

      // 根据分类代码映射基金类型
      if (classificationCode.includes('1030')) {
        fundType = 'index'; // 指数型
      } else if (classificationCode.includes('1010')) {
        fundType = 'stock'; // 股票型A类
      } else if (classificationCode.includes('1020')) {
        fundType = 'stock'; // 股票型C类
      } else if (classificationCode.includes('1040')) {
        fundType = 'stock'; // 股票型D类
      }

      return {
        id: `${fundCode}-${index}`,
        name: fundName,
        code: fundCode,
        type: fundType as "stock" | "bond" | "hybrid" | "index" | "money",
        company,
        description: `${company}管理的${getTypeLabel(fundType)}基金`,
      };
    }).filter(Boolean); // 过滤掉null值

    console.log(`成功解析基金数据: ${funds.length} 条记录`);

    return NextResponse.json({
      success: true,
      data: funds,
      total: data.iTotalRecords || funds.length,
      page: pageNum,
      size: pageSize,
    });
  } catch (error) {
    console.error("基金搜索失败:", error);
    const errorMessage = error instanceof Error ? error.message : "基金搜索失败";
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

function getTypeLabel(type: string): string {
  const typeLabels: Record<string, string> = {
    "stock": "股票型",
    "bond": "债券型",
    "hybrid": "混合型",
    "index": "指数型",
    "money": "货币型",
  };

  return typeLabels[type] || "混合型";
}
