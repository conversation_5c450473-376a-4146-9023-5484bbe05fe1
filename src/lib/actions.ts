"use server";

import { searchFunds, validateFund } from "@/lib/mock-data";
import type { Fund } from "@/types/fund";

/**
 * Server action to get all funds
 * 返回空数组，只有用户搜索时才显示基金选项
 */
export async function getFundsAction(): Promise<Fund[]> {
  try {
    // 不再返回默认基金数据，用户需要主动搜索
    return [];
  } catch (error) {
    console.error("Failed to load funds:", error);
    throw new Error("Failed to load funds");
  }
}

/**
 * Server action to search funds
 */
export async function searchFundsAction(query: string): Promise<Fund[]> {
  try {
    if (!query.trim()) {
      return [];
    }
    return await searchFunds(query);
  } catch (error) {
    console.error("Failed to search funds:", error);
    throw new Error("Failed to search funds");
  }
}

/**
 * Server action to validate fund code
 */
export async function validateFundAction(fundCode: string): Promise<boolean> {
  try {
    if (!/^\d{6}$/.test(fundCode)) {
      return false;
    }
    return await validateFund(fundCode);
  } catch (error) {
    console.error("Failed to validate fund:", error);
    return false;
  }
}
